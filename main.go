package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	log.Println("MoreLogin自动化URL爬虫启动...")

	// 加载配置
	config, err := LoadConfig("config.json")
	if err != nil {
		log.Printf("加载配置失败，使用默认配置: %v", err)
		config, _ = LoadConfig("") // 使用默认配置
	}

	// 创建高级爬虫实例
	crawler := NewAdvancedCrawler(config)
	defer crawler.Close()

	// 设置信号处理，优雅关闭
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigChan
		log.Println("收到退出信号，正在关闭...")
		crawler.Close()
		os.Exit(0)
	}()

	// 从文件加载URL
	err = crawler.LoadURLs()
	if err != nil {
		log.Fatalf("加载URL文件失败: %v", err)
	}

	// 启动MoreLogin浏览器
	err = crawler.StartBrowser()
	if err != nil {
		log.Fatalf("启动MoreLogin失败: %v", err)
	}

	// 等待用户确认浏览器已启动
	fmt.Print("请确认MoreLogin浏览器已启动并准备就绪，按回车键继续...")
	bufio.NewReader(os.Stdin).ReadBytes('\n')

	// 开始爬取所有URL
	err = crawler.CrawlAllURLs()
	if err != nil {
		log.Fatalf("爬取URL失败: %v", err)
	}

	log.Println("程序执行完成，按回车键退出...")
	bufio.NewReader(os.Stdin).ReadBytes('\n')
}
