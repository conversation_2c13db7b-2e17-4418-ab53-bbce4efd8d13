# MoreLogin自动化爬虫使用指南

## 功能特性

### 🚀 MoreLogin集成
- **本地API支持**: 通过MoreLogin本地API启动和管理配置文件
- **传统启动方式**: 支持命令行参数启动MoreLogin
- **自动配置文件选择**: 如果未指定配置文件ID，自动选择第一个可用配置文件
- **健康检查**: 自动检查API状态，失败时回退到传统启动方式

### 🌐 浏览器控制
- **新标签页模式**: 每个URL在新标签页中打开
- **标签页管理**: 可配置访问后自动关闭标签页
- **页面等待**: 可配置页面加载等待时间
- **截图功能**: 自动截取页面截图并保存

### 📊 高级功能
- **重试机制**: 支持失败重试，提高成功率
- **详细日志**: 完整的访问日志和统计信息
- **配置灵活**: 通过JSON文件配置所有参数
- **优雅关闭**: 支持API方式关闭配置文件

## 配置说明

### MoreLogin配置

```json
{
  "morelogin": {
    "execute_path": "D:\\MoreLogin\\MoreLogin.exe",  // MoreLogin可执行文件路径
    "profile_id": "",                                // 指定配置文件ID，留空自动选择
    "profile_name": "",                              // 配置文件名称（仅用于显示）
    "debug_port": 9222,                              // Chrome调试端口
    "api_port": 35000,                               // MoreLogin API端口
    "api_endpoint": "http://127.0.0.1:35000",        // API端点地址
    "auto_start": true,                              // 是否自动启动
    "wait_time": 10,                                 // 等待启动时间（秒）
    "use_local_api": true,                           // 是否使用本地API
    "headless_mode": false,                          // 是否无头模式
    "window_size": "1920,1080",                      // 窗口大小
    "user_data_dir": ""                              // 用户数据目录
  }
}
```

### 爬虫配置

```json
{
  "crawler": {
    "delay_between_requests": 3,      // URL间访问间隔（秒）
    "url_file": "url.txt",            // URL文件路径
    "max_retries": 3,                 // 最大重试次数
    "timeout": 30,                    // 单个URL超时时间（秒）
    "open_in_new_tab": true,          // 是否在新标签页打开
    "close_tab_after_visit": false,   // 访问后是否关闭标签页
    "wait_for_page_load": 3,          // 页面加载等待时间（秒）
    "take_screenshot": false,         // 是否截图
    "screenshot_dir": "screenshots"   // 截图保存目录
  }
}
```

## 使用方法

### 1. 基本使用

```bash
# 使用默认配置运行
go run .

# 或编译后运行
go build -o crawler.exe .
./crawler.exe
```

### 2. 配置MoreLogin

#### 方法一：使用本地API（推荐）

1. 确保MoreLogin已启动并开启本地API
2. 在config.json中设置：
   ```json
   {
     "morelogin": {
       "use_local_api": true,
       "api_endpoint": "http://127.0.0.1:35000"
     }
   }
   ```

#### 方法二：传统启动方式

1. 在config.json中设置MoreLogin路径：
   ```json
   {
     "morelogin": {
       "execute_path": "C:\\Program Files\\MoreLogin\\MoreLogin.exe",
       "use_local_api": false
     }
   }
   ```

### 3. 准备URL文件

在url.txt文件中添加要访问的URL：

```
https://www.example1.com
https://www.example2.com
# 这是注释，会被忽略
https://www.example3.com
```

### 4. 运行爬虫

程序会自动：
1. 检查MoreLogin API状态
2. 启动或连接到MoreLogin配置文件
3. 依次访问URL列表中的网址
4. 生成详细的访问报告

## 高级功能

### 截图功能

启用截图功能：

```json
{
  "crawler": {
    "take_screenshot": true,
    "screenshot_dir": "screenshots"
  }
}
```

截图文件命名格式：`YYYYMMDD_HHMMSS_网站名称.png`

### 新标签页模式

每个URL在新标签页中打开，避免页面间干扰：

```json
{
  "crawler": {
    "open_in_new_tab": true,
    "close_tab_after_visit": true
  }
}
```

### 自定义等待时间

根据网站加载速度调整等待时间：

```json
{
  "crawler": {
    "wait_for_page_load": 5,
    "timeout": 60
  }
}
```

## 故障排除

### 1. API连接失败

```
错误: API健康检查失败
```

**解决方案:**
- 确认MoreLogin已启动
- 检查API端口是否正确
- 确认防火墙未阻止连接

### 2. 配置文件启动失败

```
错误: 启动配置文件失败
```

**解决方案:**
- 检查配置文件ID是否正确
- 确认配置文件未被其他程序使用
- 尝试手动启动配置文件

### 3. URL访问失败

```
访问失败: context deadline exceeded
```

**解决方案:**
- 增加超时时间设置
- 检查网络连接
- 确认目标网站可访问

## 最佳实践

1. **合理设置访问间隔**: 避免对目标网站造成压力
2. **使用本地API**: 提供更好的控制和稳定性
3. **启用截图**: 便于验证访问结果
4. **监控日志**: 及时发现和解决问题
5. **定期更新配置**: 根据需要调整参数

## 示例配置

### 电商网站爬取

```json
{
  "morelogin": {
    "use_local_api": true,
    "headless_mode": false,
    "window_size": "1920,1080"
  },
  "crawler": {
    "delay_between_requests": 5,
    "timeout": 60,
    "open_in_new_tab": true,
    "take_screenshot": true,
    "wait_for_page_load": 5
  }
}
```

### 快速批量访问

```json
{
  "morelogin": {
    "use_local_api": true,
    "headless_mode": true
  },
  "crawler": {
    "delay_between_requests": 1,
    "timeout": 30,
    "open_in_new_tab": false,
    "take_screenshot": false,
    "wait_for_page_load": 2
  }
}
```
