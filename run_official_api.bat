@echo off
echo MoreLogin官方API自动化爬虫
echo ===============================

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go语言环境，请先安装Go
    pause
    exit /b 1
)

REM 检查url.txt文件是否存在
if not exist "url.txt" (
    echo 错误: 未找到url.txt文件
    echo 请创建url.txt文件并添加要访问的URL
    pause
    exit /b 1
)

REM 检查配置文件是否存在
if not exist "config.json" (
    echo 错误: 未找到config.json文件
    echo 请确保配置文件包含正确的API信息
    pause
    exit /b 1
)

echo.
echo 配置检查:
echo - 环境ID: 1958771062629539840
echo - API端点: https://api.morelogin.com
echo - 使用官方API启动环境
echo.

REM 安装依赖
echo 正在安装依赖...
go mod tidy

REM 编译程序
echo 正在编译程序...
go build -o crawler.exe .
if %errorlevel% neq 0 (
    echo 编译失败
    pause
    exit /b 1
)

echo.
echo 启动说明:
echo 1. 程序将通过MoreLogin官方API启动环境
echo 2. 使用环境ID: 1958771062629539840
echo 3. API认证信息已从config.json读取
echo 4. 启动后将在该环境的浏览器窗口中访问URL
echo.

REM 运行程序
echo 正在启动MoreLogin官方API爬虫...
crawler.exe

echo.
echo 程序执行完成！
echo 请检查日志文件: crawler.log
echo.

pause
