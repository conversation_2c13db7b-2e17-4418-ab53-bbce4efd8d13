#!/bin/bash

echo "MoreLogin自动化URL爬虫"
echo "========================"

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go语言环境，请先安装Go"
    exit 1
fi

# 检查url.txt文件是否存在
if [ ! -f "url.txt" ]; then
    echo "错误: 未找到url.txt文件"
    echo "请创建url.txt文件并添加要访问的URL"
    exit 1
fi

# 安装依赖
echo "正在安装依赖..."
go mod tidy

# 编译程序
echo "正在编译程序..."
go build -o crawler .
if [ $? -ne 0 ]; then
    echo "编译失败"
    exit 1
fi

# 运行程序
echo "启动爬虫..."
./crawler

echo "按任意键退出..."
read -n 1
