<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e8d7fb0e-fba5-4974-924b-0ddb8dc07adc" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../../go-project/pkg/mod/golang.org/<EMAIL>-amd64" />
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 3
}]]></component>
  <component name="ProjectId" id="31dJzPFmYpAIIZ0pptNal2TVy2z" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Go 构建.go build main.go.executor": "Run",
    "Go 构建.go build openurl.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.GoLinterPluginOnboarding": "true",
    "RunOnceActivity.GoLinterPluginStorageMigration": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "last_opened_file_path": "E:/project/openUrl",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm"
  }
}]]></component>
  <component name="RunManager" selected="Go 构建.go build main.go">
    <configuration name="go build main.go" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="openUrl" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="FILE" />
      <package value="openurl" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/main.go" />
      <method v="2" />
    </configuration>
    <configuration name="go build openurl" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="openUrl" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="openurl" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/main.go" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Go 构建.go build main.go" />
        <item itemvalue="Go 构建.go build openurl" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-3b128438d3f6-07d2d2d66b1e-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-251.26927.50" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-GO-251.26927.50" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e8d7fb0e-fba5-4974-924b-0ddb8dc07adc" name="更改" comment="" />
      <created>1755848278464</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755848278464</updated>
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>