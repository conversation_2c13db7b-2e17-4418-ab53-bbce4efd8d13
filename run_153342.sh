#!/bin/bash

echo "MoreLogin自动化爬虫 - 启动环境153342"
echo "========================================"

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go语言环境，请先安装Go"
    exit 1
fi

# 检查url.txt文件是否存在
if [ ! -f "url.txt" ]; then
    echo "错误: 未找到url.txt文件"
    echo "请创建url.txt文件并添加要访问的URL"
    exit 1
fi

# 检查专用配置文件是否存在
if [ ! -f "config_153342.json" ]; then
    echo "警告: 未找到config_153342.json文件，将使用默认配置"
    CONFIG_FILE="config.json"
else
    CONFIG_FILE="config_153342.json"
    echo "使用专用配置文件: $CONFIG_FILE"
fi

# 安装依赖
echo "正在安装依赖..."
go mod tidy

# 编译程序
echo "正在编译程序..."
go build -o crawler .
if [ $? -ne 0 ]; then
    echo "编译失败"
    exit 1
fi

echo ""
echo "启动配置:"
echo "- 目标环境: MoreLogin配置文件 153342"
echo "- 配置文件: $CONFIG_FILE"
echo "- 截图保存: screenshots_153342/"
echo "- 日志文件: crawler_153342.log"
echo ""

# 创建截图目录
mkdir -p screenshots_153342

# 运行程序，指定配置文件ID
echo "正在启动MoreLogin环境153342..."
./crawler -config "$CONFIG_FILE" -profile 153342

echo ""
echo "程序执行完成！"
echo "请检查以下文件:"
echo "- 日志文件: crawler_153342.log"
echo "- 截图目录: screenshots_153342/"
echo ""

echo "按任意键退出..."
read -n 1
