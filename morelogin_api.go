package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

// MoreLoginAPI MoreLogin本地API客户端
type MoreLoginAPI struct {
	BaseURL    string
	HTTPClient *http.Client
}

// ProfileInfo 配置文件信息
type ProfileInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Status      string `json:"status"`
	DebugPort   int    `json:"debug_port"`
	ProcessID   int    `json:"process_id"`
	WindowsID   string `json:"windows_id"`
	CreatedTime string `json:"created_time"`
}

// StartProfileRequest 启动配置文件请求
type StartProfileRequest struct {
	ProfileID   string `json:"profile_id"`
	DebugPort   int    `json:"debug_port,omitempty"`
	HeadlessMode bool  `json:"headless_mode,omitempty"`
	WindowSize  string `json:"window_size,omitempty"`
}

// StartProfileResponse 启动配置文件响应
type StartProfileResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		DebugPort int    `json:"debug_port"`
		ProcessID int    `json:"process_id"`
		WindowsID string `json:"windows_id"`
	} `json:"data"`
}

// StopProfileRequest 停止配置文件请求
type StopProfileRequest struct {
	ProfileID string `json:"profile_id"`
}

// APIResponse 通用API响应
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// NewMoreLoginAPI 创建MoreLogin API客户端
func NewMoreLoginAPI(baseURL string) *MoreLoginAPI {
	return &MoreLoginAPI{
		BaseURL: baseURL,
		HTTPClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// makeRequest 发送HTTP请求
func (api *MoreLoginAPI) makeRequest(method, endpoint string, payload interface{}) (*http.Response, error) {
	url := api.BaseURL + endpoint
	
	var body io.Reader
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return nil, fmt.Errorf("序列化请求数据失败: %v", err)
		}
		body = bytes.NewBuffer(jsonData)
	}
	
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "MoreLogin-Crawler/1.0")
	
	resp, err := api.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	
	return resp, nil
}

// GetProfiles 获取所有配置文件列表
func (api *MoreLoginAPI) GetProfiles() ([]ProfileInfo, error) {
	resp, err := api.makeRequest("GET", "/api/v1/profile/list", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if apiResp.Code != 0 {
		return nil, fmt.Errorf("API错误: %s", apiResp.Message)
	}

	// 转换数据类型
	profilesData, _ := json.Marshal(apiResp.Data)
	var profiles []ProfileInfo
	if err := json.Unmarshal(profilesData, &profiles); err != nil {
		return nil, fmt.Errorf("解析配置文件数据失败: %v", err)
	}

	return profiles, nil
}

// FindProfileByID 根据ID查找配置文件
func (api *MoreLoginAPI) FindProfileByID(profileID string) (*ProfileInfo, error) {
	profiles, err := api.GetProfiles()
	if err != nil {
		return nil, err
	}

	for _, profile := range profiles {
		if profile.ID == profileID {
			return &profile, nil
		}
	}

	return nil, fmt.Errorf("未找到配置文件ID: %s", profileID)
}

// StartProfile 启动指定配置文件
func (api *MoreLoginAPI) StartProfile(profileID string, debugPort int, headless bool, windowSize string) (*StartProfileResponse, error) {
	request := StartProfileRequest{
		ProfileID:    profileID,
		DebugPort:    debugPort,
		HeadlessMode: headless,
		WindowSize:   windowSize,
	}
	
	resp, err := api.makeRequest("POST", "/api/v1/profile/start", request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	var startResp StartProfileResponse
	if err := json.NewDecoder(resp.Body).Decode(&startResp); err != nil {
		return nil, fmt.Errorf("解析启动响应失败: %v", err)
	}
	
	if startResp.Code != 0 {
		return nil, fmt.Errorf("启动配置文件失败: %s", startResp.Message)
	}
	
	return &startResp, nil
}

// StopProfile 停止指定配置文件
func (api *MoreLoginAPI) StopProfile(profileID string) error {
	request := StopProfileRequest{
		ProfileID: profileID,
	}
	
	resp, err := api.makeRequest("POST", "/api/v1/profile/stop", request)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return fmt.Errorf("解析停止响应失败: %v", err)
	}
	
	if apiResp.Code != 0 {
		return fmt.Errorf("停止配置文件失败: %s", apiResp.Message)
	}
	
	return nil
}

// GetProfileStatus 获取配置文件状态
func (api *MoreLoginAPI) GetProfileStatus(profileID string) (*ProfileInfo, error) {
	endpoint := fmt.Sprintf("/api/v1/profile/status?profile_id=%s", profileID)
	resp, err := api.makeRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, fmt.Errorf("解析状态响应失败: %v", err)
	}
	
	if apiResp.Code != 0 {
		return nil, fmt.Errorf("获取配置文件状态失败: %s", apiResp.Message)
	}
	
	// 转换数据类型
	profileData, _ := json.Marshal(apiResp.Data)
	var profile ProfileInfo
	if err := json.Unmarshal(profileData, &profile); err != nil {
		return nil, fmt.Errorf("解析配置文件状态数据失败: %v", err)
	}
	
	return &profile, nil
}

// WaitForProfileReady 等待配置文件准备就绪
func (api *MoreLoginAPI) WaitForProfileReady(profileID string, timeout time.Duration) error {
	start := time.Now()
	for time.Since(start) < timeout {
		profile, err := api.GetProfileStatus(profileID)
		if err != nil {
			log.Printf("检查配置文件状态失败: %v", err)
			time.Sleep(2 * time.Second)
			continue
		}
		
		if profile.Status == "running" && profile.DebugPort > 0 {
			log.Printf("配置文件 %s 已准备就绪，调试端口: %d", profileID, profile.DebugPort)
			return nil
		}
		
		log.Printf("等待配置文件启动... 状态: %s", profile.Status)
		time.Sleep(2 * time.Second)
	}
	
	return fmt.Errorf("等待配置文件启动超时")
}

// CheckAPIHealth 检查API健康状态
func (api *MoreLoginAPI) CheckAPIHealth() error {
	resp, err := api.makeRequest("GET", "/api/v1/health", nil)
	if err != nil {
		return fmt.Errorf("API健康检查失败: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("API不健康，状态码: %d", resp.StatusCode)
	}
	
	return nil
}
