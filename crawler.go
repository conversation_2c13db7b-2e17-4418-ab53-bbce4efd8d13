package main

import (
	"bufio"
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"

	"github.com/chromedp/chromedp"
)

// AdvancedCrawler 高级爬虫结构体
type AdvancedCrawler struct {
	config     *Config
	urls       []string
	ctx        context.Context
	cancel     context.CancelFunc
	browserCmd *exec.Cmd
	results    []CrawlResult
}

// CrawlResult 爬取结果
type CrawlResult struct {
	URL       string        `json:"url"`
	Success   bool          `json:"success"`
	Error     string        `json:"error,omitempty"`
	Duration  time.Duration `json:"duration"`
	Timestamp time.Time     `json:"timestamp"`
}

// NewAdvancedCrawler 创建高级爬虫实例
func NewAdvancedCrawler(config *Config) *AdvancedCrawler {
	return &AdvancedCrawler{
		config:  config,
		results: make([]CrawlResult, 0),
	}
}

// LoadURLs 从文件加载URL
func (c *AdvancedCrawler) LoadURLs() error {
	file, err := os.Open(c.config.Crawler.URLFile)
	if err != nil {
		return fmt.Errorf("无法打开URL文件 %s: %v", c.config.Crawler.URLFile, err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineNum := 0
	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		
		// 跳过空行和注释行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		
		// 简单的URL验证
		if !strings.HasPrefix(line, "http://") && !strings.HasPrefix(line, "https://") {
			log.Printf("警告: 第%d行不是有效的URL: %s", lineNum, line)
			continue
		}
		
		c.urls = append(c.urls, line)
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取文件错误: %v", err)
	}

	log.Printf("成功加载 %d 个URL", len(c.urls))
	return nil
}

// StartBrowser 启动MoreLogin浏览器
func (c *AdvancedCrawler) StartBrowser() error {
	log.Println("正在启动MoreLogin指纹浏览器...")

	// 确定MoreLogin可执行文件路径
	execPath := c.config.MoreLogin.ExecutePath
	if execPath == "" {
		switch runtime.GOOS {
		case "windows":
			execPath = "C:\\Program Files\\MoreLogin\\MoreLogin.exe"
		case "darwin":
			execPath = "/Applications/MoreLogin.app/Contents/MacOS/MoreLogin"
		case "linux":
			execPath = "/opt/MoreLogin/MoreLogin"
		default:
			return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
		}
	}

	// 检查文件是否存在
	if _, err := os.Stat(execPath); os.IsNotExist(err) {
		return fmt.Errorf("MoreLogin可执行文件不存在: %s", execPath)
	}

	// 构建启动命令
	args := []string{
		"--remote-debugging-port", fmt.Sprintf("%d", c.config.MoreLogin.DebugPort),
		"--disable-web-security",
		"--disable-features=VizDisplayCompositor",
	}

	if c.config.MoreLogin.ProfileID != "" {
		args = append(args, "--profile-id", c.config.MoreLogin.ProfileID)
	}

	c.browserCmd = exec.Command(execPath, args...)
	
	err := c.browserCmd.Start()
	if err != nil {
		return fmt.Errorf("启动MoreLogin失败: %v", err)
	}

	log.Printf("MoreLogin已启动，PID: %d", c.browserCmd.Process.Pid)

	// 等待浏览器启动
	time.Sleep(8 * time.Second)

	// 创建Chrome上下文
	return c.createChromeContext()
}

// createChromeContext 创建Chrome调试上下文
func (c *AdvancedCrawler) createChromeContext() error {
	// 连接到现有的Chrome实例
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("remote-debugging-port", fmt.Sprintf("%d", c.config.MoreLogin.DebugPort)),
	)

	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	c.cancel = cancel

	// 创建上下文
	ctx, _ := chromedp.NewContext(allocCtx)
	c.ctx = ctx

	// 测试连接
	var title string
	err := chromedp.Run(c.ctx,
		chromedp.Navigate("about:blank"),
		chromedp.Title(&title),
	)

	if err != nil {
		return fmt.Errorf("无法连接到Chrome调试端口: %v", err)
	}

	log.Println("成功连接到MoreLogin浏览器")
	return nil
}

// VisitURL 访问单个URL并记录结果
func (c *AdvancedCrawler) VisitURL(url string) CrawlResult {
	start := time.Now()
	result := CrawlResult{
		URL:       url,
		Timestamp: start,
	}

	log.Printf("正在访问: %s", url)

	// 设置超时上下文
	ctx, cancel := context.WithTimeout(c.ctx, time.Duration(c.config.Crawler.Timeout)*time.Second)
	defer cancel()

	// 访问URL
	err := chromedp.Run(ctx,
		chromedp.Navigate(url),
		chromedp.WaitVisible("body", chromedp.ByQuery),
	)

	result.Duration = time.Since(start)

	if err != nil {
		result.Success = false
		result.Error = err.Error()
		log.Printf("访问失败 %s: %v", url, err)
	} else {
		result.Success = true
		log.Printf("访问成功: %s (耗时: %v)", url, result.Duration)
	}

	return result
}

// CrawlAllURLs 爬取所有URL
func (c *AdvancedCrawler) CrawlAllURLs() error {
	if len(c.urls) == 0 {
		return fmt.Errorf("没有URL需要访问")
	}

	log.Printf("开始访问 %d 个URL，每个URL间隔 %d 秒", len(c.urls), c.config.Crawler.DelayBetweenRequests)

	for i, url := range c.urls {
		log.Printf("进度: %d/%d", i+1, len(c.urls))

		// 尝试访问URL，支持重试
		var result CrawlResult
		for retry := 0; retry <= c.config.Crawler.MaxRetries; retry++ {
			if retry > 0 {
				log.Printf("重试第 %d 次: %s", retry, url)
				time.Sleep(2 * time.Second)
			}

			result = c.VisitURL(url)
			if result.Success {
				break
			}
		}

		c.results = append(c.results, result)

		// 等待指定时间再访问下一个URL
		if i < len(c.urls)-1 {
			time.Sleep(time.Duration(c.config.Crawler.DelayBetweenRequests) * time.Second)
		}
	}

	c.printSummary()
	return nil
}

// printSummary 打印爬取摘要
func (c *AdvancedCrawler) printSummary() {
	successful := 0
	failed := 0
	totalDuration := time.Duration(0)

	for _, result := range c.results {
		if result.Success {
			successful++
		} else {
			failed++
		}
		totalDuration += result.Duration
	}

	log.Println("=== 爬取摘要 ===")
	log.Printf("总URL数: %d", len(c.results))
	log.Printf("成功: %d", successful)
	log.Printf("失败: %d", failed)
	log.Printf("总耗时: %v", totalDuration)
	log.Printf("平均耗时: %v", totalDuration/time.Duration(len(c.results)))
}

// Close 关闭爬虫并清理资源
func (c *AdvancedCrawler) Close() {
	if c.cancel != nil {
		c.cancel()
	}
	
	if c.browserCmd != nil && c.browserCmd.Process != nil {
		log.Println("正在关闭MoreLogin浏览器...")
		c.browserCmd.Process.Kill()
	}
	
	log.Println("爬虫已关闭")
}
