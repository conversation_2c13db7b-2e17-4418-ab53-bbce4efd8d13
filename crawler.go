package main

import (
    "bufio"
    "context"
    "fmt"
    "log"
    "os"
    "os/exec"
    "strings"
    "time"

    "github.com/chromedp/chromedp"
)

// AdvancedCrawler 高级爬虫结构体
type AdvancedCrawler struct {
    config       *Config
    urls         []string
    ctx          context.Context
    cancel       context.CancelFunc
    browserCmd   *exec.Cmd
    results      []CrawlResult
    moreLoginAPI *MoreLoginAPI
    officialAPI  *MoreLoginOfficialAPI
    profileInfo  *ProfileInfo
    currentTabID string
    startedEnvID string // 记录启动的环境ID
}

// CrawlResult 爬取结果
type CrawlResult struct {
    URL       string        `json:"url"`
    Success   bool          `json:"success"`
    Error     string        `json:"error,omitempty"`
    Duration  time.Duration `json:"duration"`
    Timestamp time.Time     `json:"timestamp"`
}

// NewAdvancedCrawler 创建高级爬虫实例
func NewAdvancedCrawler(config *Config) *AdvancedCrawler {
    crawler := &AdvancedCrawler{
        config:  config,
        results: make([]CrawlResult, 0),
    }

    // 如果启用了官方API，创建官方API客户端
    if config.MoreLogin.UseOfficialAPI {
        crawler.officialAPI = NewMoreLoginOfficialAPI(
            config.MoreLogin.APIEndpoint,
            config.MoreLogin.APIID,
            config.MoreLogin.APIKey,
        )
    }

    // 如果启用了本地API，创建本地API客户端
    if config.MoreLogin.UseLocalAPI {
        crawler.moreLoginAPI = NewMoreLoginAPI(config.MoreLogin.LocalEndpoint)
    }

    return crawler
}

// LoadURLs 从文件加载URL
func (c *AdvancedCrawler) LoadURLs() error {
    file, err := os.Open(c.config.Crawler.URLFile)
    if err != nil {
        return fmt.Errorf("无法打开URL文件 %s: %v", c.config.Crawler.URLFile, err)
    }
    defer file.Close()

    scanner := bufio.NewScanner(file)
    lineNum := 0
    for scanner.Scan() {
        lineNum++
        line := strings.TrimSpace(scanner.Text())

        // 跳过空行和注释行
        if line == "" || strings.HasPrefix(line, "#") {
            continue
        }

        // 简单的URL验证
        if !strings.HasPrefix(line, "http://") && !strings.HasPrefix(line, "https://") {
            log.Printf("警告: 第%d行不是有效的URL: %s", lineNum, line)
            continue
        }

        c.urls = append(c.urls, line)
    }

    if err := scanner.Err(); err != nil {
        return fmt.Errorf("读取文件错误: %v", err)
    }

    log.Printf("成功加载 %d 个URL", len(c.urls))
    return nil
}

// StartBrowser 启动MoreLogin浏览器
func (c *AdvancedCrawler) StartBrowser() error {
    log.Println("正在启动MoreLogin指纹浏览器...")

    // 如果使用官方API，通过官方API启动环境
    if c.config.MoreLogin.UseOfficialAPI && c.officialAPI != nil {
        return c.startBrowserWithOfficialAPI()
    }

    // 如果使用本地API，通过本地API启动配置文件
    if c.config.MoreLogin.UseLocalAPI && c.moreLoginAPI != nil {
        return c.startBrowserWithLocalAPI()
    }

    // 传统方式启动MoreLogin
    return c.startBrowserTraditional()
}

// startBrowserWithOfficialAPI 通过官方API启动MoreLogin环境
func (c *AdvancedCrawler) startBrowserWithOfficialAPI() error {
    log.Println("使用MoreLogin官方API启动环境...")

    // 检查必要的配置
    if c.config.MoreLogin.EnvID == "" {
        return fmt.Errorf("未配置环境ID (env_id)")
    }
    if c.config.MoreLogin.APIID == "" {
        return fmt.Errorf("未配置API ID (api_id)")
    }
    if c.config.MoreLogin.APIKey == "" {
        return fmt.Errorf("未配置API Key (api_key)")
    }

    //// 测试API连接
    //if err := c.officialAPI.TestConnection(); err != nil {
    //    log.Printf("官方API连接测试失败，尝试本地API: %v", err)
    //    if c.config.MoreLogin.UseLocalAPI && c.moreLoginAPI != nil {
    //        return c.startBrowserWithLocalAPI()
    //    }
    //    return c.startBrowserTraditional()
    //}

    // 启动环境
    envID := c.config.MoreLogin.EnvID
    log.Printf("正在启动环境ID: %s", envID)

    startResp, err := c.officialAPI.StartEnvironment(envID)
    if err != nil {
        return fmt.Errorf("启动环境失败: %v", err)
    }

    // 记录启动的环境ID
    c.startedEnvID = envID

    // 等待环境准备就绪
    if err = c.officialAPI.WaitForEnvironmentReady(envID, time.Duration(c.config.MoreLogin.WaitTime)*time.Second); err != nil {
        return fmt.Errorf("等待环境准备就绪失败: %v", err)
    }

    // 设置调试端口（如果API返回了端口信息）
    if startResp.Data.DebugPort > 0 {
        c.config.MoreLogin.DebugPort = startResp.Data.DebugPort
        log.Printf("使用API返回的调试端口: %d", startResp.Data.DebugPort)
    }

    // 创建Chrome上下文
    return c.createChromeContext()
}

// startBrowserWithLocalAPI 通过本地API启动MoreLogin配置文件
func (c *AdvancedCrawler) startBrowserWithLocalAPI() error {
    log.Println("使用MoreLogin本地API启动配置文件...")

    // 检查API健康状态
    if err := c.moreLoginAPI.CheckAPIHealth(); err != nil {
        log.Printf("本地API健康检查失败，尝试传统启动方式: %v", err)
        return c.startBrowserTraditional()
    }

    // 获取配置文件ID
    profileID := c.config.MoreLogin.ProfileID
    if profileID == "" {
        profiles, err := c.moreLoginAPI.GetProfiles()
        if err != nil {
            return fmt.Errorf("获取配置文件列表失败: %v", err)
        }
        if len(profiles) == 0 {
            return fmt.Errorf("没有找到可用的配置文件")
        }
        profileID = profiles[0].ID
        log.Printf("自动选择配置文件: %s (%s)", profiles[0].Name, profileID)
    } else {
        // 验证指定的配置文件是否存在
        log.Printf("正在验证配置文件ID: %s", profileID)
        profile, err := c.moreLoginAPI.FindProfileByID(profileID)
        if err != nil {
            return fmt.Errorf("验证配置文件失败: %v", err)
        }
        log.Printf("✓ 找到配置文件: %s (ID: %s)", profile.Name, profileID)
    }

    // 启动配置文件
    log.Printf("正在启动MoreLogin配置文件: %s", profileID)
    log.Printf("启动参数: 调试端口=%d, 无头模式=%v, 窗口大小=%s",
        c.config.MoreLogin.DebugPort,
        c.config.MoreLogin.HeadlessMode,
        c.config.MoreLogin.WindowSize)

    startResp, err := c.moreLoginAPI.StartProfile(
        profileID,
        c.config.MoreLogin.DebugPort,
        c.config.MoreLogin.HeadlessMode,
        c.config.MoreLogin.WindowSize,
    )
    if err != nil {
        return fmt.Errorf("启动配置文件失败: %v", err)
    }

    log.Printf("✓ 配置文件已启动，调试端口: %d", startResp.Data.DebugPort)

    // 等待配置文件准备就绪
    if err = c.moreLoginAPI.WaitForProfileReady(profileID, time.Duration(c.config.MoreLogin.WaitTime)*time.Second); err != nil {
        return fmt.Errorf("等待配置文件准备就绪失败: %v", err)
    }

    // 获取配置文件信息
    profileInfo, err := c.moreLoginAPI.GetProfileStatus(profileID)
    if err != nil {
        return fmt.Errorf("获取配置文件状态失败: %v", err)
    }
    c.profileInfo = profileInfo

    // 更新调试端口
    c.config.MoreLogin.DebugPort = profileInfo.DebugPort

    // 创建Chrome上下文
    return c.createChromeContext()
}

// startBrowserTraditional 传统方式启动MoreLogin
func (c *AdvancedCrawler) startBrowserTraditional() error {
    log.Println("使用传统方式启动MoreLogin...")

    // 确定MoreLogin可执行文件路径
    execPath := c.config.MoreLogin.ExecutePath
    if execPath == "" {
        return fmt.Errorf("请在config.json配置execute_path, 此值为MoreLogin.exe所在的路径")
    }

    // 检查文件是否存在
    if _, err := os.Stat(execPath); os.IsNotExist(err) {
        return fmt.Errorf("MoreLogin可执行文件不存在: %s", execPath)
    }

    // 构建启动命令
    args := []string{
        "--remote-debugging-port", fmt.Sprintf("%d", c.config.MoreLogin.DebugPort),
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
    }

    if c.config.MoreLogin.ProfileID != "" {
        args = append(args, "--profile-id", c.config.MoreLogin.ProfileID)
    }

    if c.config.MoreLogin.HeadlessMode {
        args = append(args, "--headless")
    }

    if c.config.MoreLogin.WindowSize != "" {
        args = append(args, "--window-size="+c.config.MoreLogin.WindowSize)
    }

    if c.config.MoreLogin.UserDataDir != "" {
        args = append(args, "--user-data-dir="+c.config.MoreLogin.UserDataDir)
    }

    c.browserCmd = exec.Command(execPath, args...)

    err := c.browserCmd.Start()
    if err != nil {
        return fmt.Errorf("启动MoreLogin失败: %v", err)
    }

    log.Printf("MoreLogin已启动，PID: %d", c.browserCmd.Process.Pid)

    // 等待浏览器启动
    time.Sleep(time.Duration(c.config.MoreLogin.WaitTime) * time.Second)

    // 创建Chrome上下文
    return c.createChromeContext()
}

// createChromeContext 创建Chrome调试上下文
func (c *AdvancedCrawler) createChromeContext() error {
    // 连接到现有的Chrome实例
    opts := append(chromedp.DefaultExecAllocatorOptions[:],
        chromedp.Flag("remote-debugging-port", fmt.Sprintf("%d", c.config.MoreLogin.DebugPort)),
    )

    allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
    c.cancel = cancel

    // 创建上下文
    ctx, _ := chromedp.NewContext(allocCtx)
    c.ctx = ctx

    // 测试连接
    var title string
    err := chromedp.Run(c.ctx,
        chromedp.Navigate("about:blank"),
        chromedp.Title(&title),
    )

    if err != nil {
        return fmt.Errorf("无法连接到Chrome调试端口: %v", err)
    }

    log.Println("成功连接到MoreLogin浏览器")
    return nil
}

// VisitURL 访问单个URL并记录结果
func (c *AdvancedCrawler) VisitURL(url string) CrawlResult {
    start := time.Now()
    result := CrawlResult{
        URL:       url,
        Timestamp: start,
    }

    log.Printf("正在访问: %s", url)

    // 设置超时上下文
    ctx, cancel := context.WithTimeout(c.ctx, time.Duration(c.config.Crawler.Timeout)*time.Second)
    defer cancel()

    var actions []chromedp.Action

    // 如果配置为在新标签页打开
    if c.config.Crawler.OpenInNewTab {
        // 创建新标签页并导航
        actions = append(actions,
            chromedp.ActionFunc(func(ctx context.Context) error {
                // 在当前标签页中打开新URL
                return chromedp.Navigate(url).Do(ctx)
            }),
        )
    } else {
        actions = append(actions,
            chromedp.Navigate(url),
        )
    }

    // 等待页面加载
    actions = append(actions,
        chromedp.WaitVisible("body", chromedp.ByQuery),
        chromedp.Sleep(time.Duration(c.config.Crawler.WaitForPageLoad)*time.Second),
    )

    // 如果启用截图功能
    if c.config.Crawler.TakeScreenshot {
        // 确保截图目录存在
        if err := os.MkdirAll(c.config.Crawler.ScreenshotDir, 0755); err != nil {
            log.Printf("创建截图目录失败: %v", err)
        } else {
            // 生成截图文件名
            timestamp := time.Now().Format("20060102_150405")
            filename := fmt.Sprintf("%s/%s_%s.png", c.config.Crawler.ScreenshotDir, timestamp, sanitizeFilename(url))

            var buf []byte
            actions = append(actions,
                chromedp.FullScreenshot(&buf, 90),
                chromedp.ActionFunc(func(ctx context.Context) error {
                    return os.WriteFile(filename, buf, 0644)
                }),
            )
        }
    }

    // 执行所有操作
    err := chromedp.Run(ctx, actions...)

    // 注意：由于chromedp的限制，我们暂时不支持关闭特定标签页
    // 如果需要此功能，可以考虑使用其他浏览器自动化工具

    result.Duration = time.Since(start)

    if err != nil {
        result.Success = false
        result.Error = err.Error()
        log.Printf("访问失败 %s: %v", url, err)
    } else {
        result.Success = true
        log.Printf("访问成功: %s (耗时: %v)", url, result.Duration)
    }

    return result
}

// sanitizeFilename 清理文件名中的非法字符
func sanitizeFilename(url string) string {
    // 移除协议前缀
    filename := strings.ReplaceAll(url, "https://", "")
    filename = strings.ReplaceAll(filename, "http://", "")

    // 替换非法字符
    filename = strings.ReplaceAll(filename, "/", "_")
    filename = strings.ReplaceAll(filename, "\\", "_")
    filename = strings.ReplaceAll(filename, ":", "_")
    filename = strings.ReplaceAll(filename, "*", "_")
    filename = strings.ReplaceAll(filename, "?", "_")
    filename = strings.ReplaceAll(filename, "\"", "_")
    filename = strings.ReplaceAll(filename, "<", "_")
    filename = strings.ReplaceAll(filename, ">", "_")
    filename = strings.ReplaceAll(filename, "|", "_")

    // 限制长度
    if len(filename) > 100 {
        filename = filename[:100]
    }

    return filename
}

// CrawlAllURLs 爬取所有URL
func (c *AdvancedCrawler) CrawlAllURLs() error {
    if len(c.urls) == 0 {
        return fmt.Errorf("没有URL需要访问")
    }

    log.Printf("开始访问 %d 个URL，每个URL间隔 %d 秒", len(c.urls), c.config.Crawler.DelayBetweenRequests)

    for i, url := range c.urls {
        log.Printf("进度: %d/%d", i+1, len(c.urls))

        // 尝试访问URL，支持重试
        var result CrawlResult
        for retry := 0; retry <= c.config.Crawler.MaxRetries; retry++ {
            if retry > 0 {
                log.Printf("重试第 %d 次: %s", retry, url)
                time.Sleep(2 * time.Second)
            }

            result = c.VisitURL(url)
            if result.Success {
                break
            }
        }

        c.results = append(c.results, result)

        // 等待指定时间再访问下一个URL
        if i < len(c.urls)-1 {
            time.Sleep(time.Duration(c.config.Crawler.DelayBetweenRequests) * time.Second)
        }
    }

    c.printSummary()
    return nil
}

// printSummary 打印爬取摘要
func (c *AdvancedCrawler) printSummary() {
    successful := 0
    failed := 0
    totalDuration := time.Duration(0)

    for _, result := range c.results {
        if result.Success {
            successful++
        } else {
            failed++
        }
        totalDuration += result.Duration
    }

    log.Println("=== 爬取摘要 ===")
    log.Printf("总URL数: %d", len(c.results))
    log.Printf("成功: %d", successful)
    log.Printf("失败: %d", failed)
    log.Printf("总耗时: %v", totalDuration)
    log.Printf("平均耗时: %v", totalDuration/time.Duration(len(c.results)))
}

// Close 关闭爬虫并清理资源
func (c *AdvancedCrawler) Close() {
    if c.cancel != nil {
        c.cancel()
    }

    // 如果使用官方API启动，通过官方API关闭
    if c.config.MoreLogin.UseOfficialAPI && c.officialAPI != nil && c.startedEnvID != "" {
        log.Printf("正在通过官方API关闭环境: %s", c.startedEnvID)
        if err := c.officialAPI.StopEnvironment(c.startedEnvID); err != nil {
            log.Printf("通过官方API关闭环境失败: %v", err)
        } else {
            log.Println("环境已通过官方API关闭")
        }
    }

    // 如果使用本地API启动，通过本地API关闭
    if c.config.MoreLogin.UseLocalAPI && c.moreLoginAPI != nil && c.profileInfo != nil {
        log.Printf("正在通过本地API关闭配置文件: %s", c.profileInfo.ID)
        if err := c.moreLoginAPI.StopProfile(c.profileInfo.ID); err != nil {
            log.Printf("通过本地API关闭配置文件失败: %v", err)
        } else {
            log.Println("配置文件已通过本地API关闭")
        }
    }

    // 传统方式关闭
    if c.browserCmd != nil && c.browserCmd.Process != nil {
        log.Println("正在关闭MoreLogin浏览器...")
        c.browserCmd.Process.Kill()
    }

    log.Println("爬虫已关闭")
}
