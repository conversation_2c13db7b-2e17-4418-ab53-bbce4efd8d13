package main

import (
	"encoding/json"
	"fmt"
	"os"
)

// Config 应用程序配置
type Config struct {
	MoreLogin struct {
		ExecutePath string `json:"execute_path"`
		ProfileID   string `json:"profile_id"`
		DebugPort   int    `json:"debug_port"`
	} `json:"morelogin"`
	
	Crawler struct {
		DelayBetweenRequests int    `json:"delay_between_requests"` // 秒
		URLFile              string `json:"url_file"`
		MaxRetries           int    `json:"max_retries"`
		Timeout              int    `json:"timeout"` // 秒
	} `json:"crawler"`
	
	Logging struct {
		Level    string `json:"level"`
		LogFile  string `json:"log_file"`
		Console  bool   `json:"console"`
	} `json:"logging"`
}

// LoadConfig 从文件加载配置
func LoadConfig(filename string) (*Config, error) {
	config := &Config{}
	
	// 设置默认值
	config.MoreLogin.DebugPort = 9222
	config.Crawler.DelayBetweenRequests = 3
	config.Crawler.URLFile = "url.txt"
	config.Crawler.MaxRetries = 3
	config.Crawler.Timeout = 30
	config.Logging.Level = "info"
	config.Logging.Console = true
	
	// 如果配置文件存在，则加载
	if _, err := os.Stat(filename); err == nil {
		file, err := os.Open(filename)
		if err != nil {
			return nil, fmt.Errorf("无法打开配置文件: %v", err)
		}
		defer file.Close()
		
		decoder := json.NewDecoder(file)
		if err := decoder.Decode(config); err != nil {
			return nil, fmt.Errorf("解析配置文件失败: %v", err)
		}
	}
	
	return config, nil
}

// SaveConfig 保存配置到文件
func (c *Config) SaveConfig(filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("无法创建配置文件: %v", err)
	}
	defer file.Close()
	
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(c); err != nil {
		return fmt.Errorf("保存配置文件失败: %v", err)
	}
	
	return nil
}
