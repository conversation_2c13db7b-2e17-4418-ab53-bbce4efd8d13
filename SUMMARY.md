# MoreLogin自动化爬虫项目总结

## 🎯 项目目标
启动MoreLogin中序号为153342的环境，并在该环境的浏览器窗口中访问URL列表。

## ✅ 已实现功能

### 1. MoreLogin环境管理
- ✅ 支持通过本地API启动指定配置文件
- ✅ 支持传统命令行方式启动
- ✅ 自动验证配置文件153342是否存在
- ✅ 智能回退机制（API失败时使用传统方式）

### 2. 浏览器窗口控制
- ✅ 在指定环境的浏览器窗口中打开URL
- ✅ 支持窗口大小配置（1920x1080）
- ✅ 支持显示模式（非无头模式，可见窗口）
- ✅ 页面加载等待机制

### 3. URL访问功能
- ✅ 从url.txt文件读取URL列表
- ✅ 依次访问每个URL
- ✅ 可配置访问间隔时间
- ✅ 智能重试机制
- ✅ 详细的访问统计

### 4. 截图和日志
- ✅ 自动截取每个页面的完整截图
- ✅ 专用截图目录（screenshots_153342/）
- ✅ 详细的执行日志（crawler_153342.log）
- ✅ 实时控制台输出

### 5. 配置和启动
- ✅ 专用配置文件（config_153342.json）
- ✅ 命令行参数支持
- ✅ 一键启动脚本（run_153342.bat/sh）
- ✅ 帮助信息和使用说明

## 📁 项目文件结构

```
openUrl/
├── 核心程序文件
│   ├── main.go              // 主程序入口，支持命令行参数
│   ├── crawler.go           // 高级爬虫实现
│   ├── morelogin_api.go     // MoreLogin API客户端
│   └── config.go            // 配置文件处理
│
├── 配置文件
│   ├── config.json          // 通用配置文件
│   └── config_153342.json   // 环境153342专用配置
│
├── 启动脚本
│   ├── run_153342.bat       // Windows启动脚本
│   └── run_153342.sh        // Linux/macOS启动脚本
│
├── 文档说明
│   ├── README.md            // 项目总体说明
│   ├── README_153342.md     // 环境153342使用指南
│   ├── morelogin_usage.md   // MoreLogin使用指南
│   └── example_usage.md     // 使用示例
│
└── 数据文件
    ├── url.txt              // URL列表文件
    └── go.mod               // Go模块文件
```

## 🚀 启动方式

### 方式一：一键启动（推荐）
```bash
# Windows
run_153342.bat

# Linux/macOS
./run_153342.sh
```

### 方式二：命令行启动
```bash
# 指定配置文件ID
go run . -profile 153342

# 使用专用配置文件
go run . -config config_153342.json -profile 153342
```

### 方式三：编译后启动
```bash
go build -o crawler.exe .
crawler.exe -profile 153342
```

## 🔧 核心配置

### MoreLogin环境153342配置
```json
{
  "morelogin": {
    "profile_id": "153342",           // 指定环境153342
    "use_local_api": true,            // 优先使用本地API
    "headless_mode": false,           // 显示浏览器窗口
    "window_size": "1920,1080",       // 窗口大小
    "wait_time": 15                   // 等待启动时间
  }
}
```

### 爬虫行为配置
```json
{
  "crawler": {
    "delay_between_requests": 3,      // URL间隔3秒
    "open_in_new_tab": false,         // 在同一窗口访问
    "take_screenshot": true,          // 启用截图
    "screenshot_dir": "screenshots_153342",
    "wait_for_page_load": 5           // 页面加载等待5秒
  }
}
```

## 📊 执行流程

1. **环境验证** → 检查Go环境和必要文件
2. **配置加载** → 读取config_153342.json
3. **API连接** → 连接MoreLogin本地API
4. **环境验证** → 确认环境153342存在
5. **启动环境** → 启动MoreLogin配置文件153342
6. **浏览器连接** → 连接到浏览器调试端口
7. **URL访问** → 在环境窗口中依次访问URL
8. **结果保存** → 截图和日志记录
9. **资源清理** → 优雅关闭环境

## 📈 输出结果

### 日志文件
- **位置**: `crawler_153342.log`
- **内容**: 详细执行日志、错误信息、性能统计

### 截图文件
- **位置**: `screenshots_153342/`
- **格式**: `YYYYMMDD_HHMMSS_网站名称.png`
- **内容**: 每个访问页面的完整截图

### 控制台输出
- 实时显示启动过程
- URL访问进度和结果
- 详细的统计摘要

## 🛡️ 错误处理

1. **API连接失败** → 自动回退到传统启动方式
2. **环境不存在** → 清晰的错误提示和解决建议
3. **URL访问失败** → 自动重试机制
4. **资源清理** → 程序异常退出时自动清理

## 🎉 项目特色

1. **智能双模式**: API优先，传统备用
2. **专用配置**: 针对环境153342优化
3. **可视化操作**: 在真实浏览器窗口中执行
4. **完整记录**: 截图+日志双重记录
5. **一键启动**: 简化操作流程
6. **错误友好**: 详细的错误提示和解决方案

## 📝 使用建议

1. **首次使用**: 建议先运行 `run_153342.bat` 进行测试
2. **生产环境**: 使用 `go build` 编译后运行
3. **批量处理**: 可以修改url.txt文件批量处理不同URL
4. **定时任务**: 可以配置定时任务自动执行
5. **监控日志**: 定期检查日志文件确保正常运行

这个项目完全满足了你的需求：启动MoreLogin中序号为153342的环境，并在该环境的浏览器窗口中访问URL列表。
