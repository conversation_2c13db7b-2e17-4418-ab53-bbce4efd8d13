@echo off
echo MoreLogin两步启动流程
echo ========================

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go语言环境，请先安装Go
    pause
    exit /b 1
)

REM 检查url.txt文件是否存在
if not exist "url.txt" (
    echo 错误: 未找到url.txt文件
    echo 请创建url.txt文件并添加要访问的URL
    pause
    exit /b 1
)

REM 检查配置文件是否存在
if not exist "config.json" (
    echo 错误: 未找到config.json文件
    echo 请确保配置文件包含正确的API信息
    pause
    exit /b 1
)

echo.
echo 启动流程说明:
echo ===============
echo 第一步: 启动MoreLogin应用程序
echo   - 检查MoreLogin是否已运行
echo   - 如果未运行，自动启动MoreLogin主程序
echo   - 等待MoreLogin完全启动并准备就绪
echo.
echo 第二步: 通过API启动指定环境
echo   - 环境ID: 1958771062629539840
echo   - 使用本地API: http://127.0.0.1:40000
echo   - 启动环境并等待浏览器准备就绪
echo.
echo 第三步: 在环境浏览器中访问URL
echo   - 依次访问url.txt中的所有URL
echo   - 记录访问结果和统计信息
echo.

REM 安装依赖
echo 正在安装依赖...
go mod tidy

REM 编译程序
echo 正在编译程序...
go build -o crawler.exe .
if %errorlevel% neq 0 (
    echo 编译失败
    pause
    exit /b 1
)

echo.
echo 开始执行两步启动流程...
echo ===========================

REM 运行程序
crawler.exe

echo.
echo 程序执行完成！
echo.
echo 执行流程总结:
echo 1. MoreLogin应用程序启动 ✓
echo 2. 环境启动和浏览器连接 ✓  
echo 3. URL访问和结果记录 ✓
echo 4. 环境关闭和资源清理 ✓
echo.
echo 请检查日志文件: crawler.log
echo.

pause
