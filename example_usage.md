# 使用示例

## 快速开始

### 1. 准备URL文件

编辑 `url.txt` 文件，添加要访问的URL：

```
https://www.google.com
https://www.github.com
https://www.stackoverflow.com
```

### 2. 配置MoreLogin路径

如果MoreLogin安装在非默认位置，请编辑 `config.json`：

```json
{
  "morelogin": {
    "execute_path": "D:\\MoreLogin\\MoreLogin.exe",
    "profile_id": "your-profile-id",
    "debug_port": 9222
  }
}
```

### 3. 运行程序

**Windows:**
```cmd
run.bat
```

**Linux/macOS:**
```bash
./run.sh
```

**手动运行:**
```bash
go run .
```

## 高级配置示例

### 自定义访问间隔和重试

```json
{
  "morelogin": {
    "execute_path": "",
    "profile_id": "",
    "debug_port": 9222
  },
  "crawler": {
    "delay_between_requests": 5,
    "url_file": "url.txt",
    "max_retries": 5,
    "timeout": 60
  },
  "logging": {
    "level": "debug",
    "log_file": "crawler.log",
    "console": true
  }
}
```

### 使用特定的指纹浏览器配置文件

```json
{
  "morelogin": {
    "execute_path": "C:\\Program Files\\MoreLogin\\MoreLogin.exe",
    "profile_id": "profile_123456",
    "debug_port": 9222
  }
}
```

## 运行流程示例

```
MoreLogin自动化URL爬虫启动...
成功加载 3 个URL
正在启动MoreLogin指纹浏览器...
MoreLogin已启动，PID: 12345
成功连接到MoreLogin浏览器
请确认MoreLogin浏览器已启动并准备就绪，按回车键继续...

开始访问 3 个URL，每个URL间隔 3 秒
进度: 1/3
正在访问: https://www.google.com
访问成功: https://www.google.com (耗时: 2.5s)
进度: 2/3
正在访问: https://www.github.com
访问成功: https://www.github.com (耗时: 3.2s)
进度: 3/3
正在访问: https://www.stackoverflow.com
访问成功: https://www.stackoverflow.com (耗时: 2.8s)

=== 爬取摘要 ===
总URL数: 3
成功: 3
失败: 0
总耗时: 8.5s
平均耗时: 2.83s

程序执行完成，按回车键退出...
```

## 常见使用场景

### 1. 批量访问电商网站

```
# url.txt
https://www.amazon.com/product1
https://www.amazon.com/product2
https://www.ebay.com/item1
https://www.etsy.com/listing1
```

### 2. 社交媒体自动化

```
# url.txt
https://www.facebook.com/page1
https://www.instagram.com/profile1
https://www.twitter.com/user1
```

### 3. 新闻网站监控

```
# url.txt
https://news.ycombinator.com
https://www.reddit.com/r/programming
https://techcrunch.com
```

## 故障排除示例

### 问题1: MoreLogin启动失败

```
错误: 启动MoreLogin失败: exec: "C:\Program Files\MoreLogin\MoreLogin.exe": file does not exist
```

**解决方案:**
1. 检查MoreLogin是否已安装
2. 确认安装路径是否正确
3. 更新 `config.json` 中的路径

### 问题2: 无法连接到Chrome调试端口

```
错误: 无法连接到Chrome调试端口: dial tcp 127.0.0.1:9222: connect: connection refused
```

**解决方案:**
1. 确认端口9222未被占用
2. 检查防火墙设置
3. 尝试更改调试端口

### 问题3: URL访问超时

```
访问失败 https://example.com: context deadline exceeded
```

**解决方案:**
1. 增加超时时间设置
2. 检查网络连接
3. 确认目标网站是否可访问

## 性能优化建议

1. **合理设置访问间隔**: 避免对目标网站造成压力
2. **调整超时时间**: 根据网络情况和目标网站响应时间调整
3. **使用合适的重试次数**: 平衡成功率和执行时间
4. **监控资源使用**: 注意内存和CPU使用情况

## 扩展功能示例

可以基于现有代码扩展以下功能：

1. **数据提取**: 提取页面标题、内容等
2. **截图功能**: 保存页面截图
3. **表单填写**: 自动填写表单
4. **Cookie管理**: 保存和恢复Cookie
5. **代理支持**: 使用代理服务器
