@echo off
echo MoreLogin自动化URL爬虫 v2.0
echo ================================

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go语言环境，请先安装Go
    pause
    exit /b 1
)

REM 检查url.txt文件是否存在
if not exist "url.txt" (
    echo 错误: 未找到url.txt文件
    echo 请创建url.txt文件并添加要访问的URL
    pause
    exit /b 1
)

REM 检查config.json文件是否存在
if not exist "config.json" (
    echo 警告: 未找到config.json文件，将使用默认配置
)

REM 安装依赖
echo 正在安装依赖...
go mod tidy

REM 编译程序
echo 正在编译程序...
go build -o crawler.exe .
if %errorlevel% neq 0 (
    echo 编译失败
    pause
    exit /b 1
)

echo.
echo 功能特性:
echo - 支持MoreLogin本地API和传统启动方式
echo - 新标签页模式访问URL
echo - 自动截图功能
echo - 详细访问统计
echo - 智能重试机制
echo.

REM 运行程序
echo 启动爬虫...
crawler.exe

pause
