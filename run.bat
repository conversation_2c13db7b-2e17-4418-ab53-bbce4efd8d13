@echo off
echo MoreLogin自动化URL爬虫
echo ========================

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go语言环境，请先安装Go
    pause
    exit /b 1
)

REM 检查url.txt文件是否存在
if not exist "url.txt" (
    echo 错误: 未找到url.txt文件
    echo 请创建url.txt文件并添加要访问的URL
    pause
    exit /b 1
)

REM 安装依赖
echo 正在安装依赖...
go mod tidy

REM 编译程序
echo 正在编译程序...
go build -o crawler.exe .
if %errorlevel% neq 0 (
    echo 编译失败
    pause
    exit /b 1
)

REM 运行程序
echo 启动爬虫...
crawler.exe

pause
