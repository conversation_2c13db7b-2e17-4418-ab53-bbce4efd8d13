# MoreLogin环境153342自动化使用指南

## 快速开始

### 1. 启动MoreLogin环境153342

#### Windows用户
```cmd
run_153342.bat
```

#### Linux/macOS用户
```bash
chmod +x run_153342.sh
./run_153342.sh
```

#### 手动启动
```bash
# 使用专用配置文件
go run . -config config_153342.json -profile 153342

# 或者直接指定配置文件ID
go run . -profile 153342
```

### 2. 程序执行流程

1. **验证环境**: 检查Go环境和必要文件
2. **加载配置**: 读取config_153342.json配置文件
3. **连接MoreLogin**: 通过本地API连接MoreLogin
4. **验证配置文件**: 确认环境153342存在且可用
5. **启动环境**: 启动MoreLogin配置文件153342
6. **等待就绪**: 等待浏览器环境完全启动
7. **访问URL**: 在该环境的浏览器窗口中依次访问URL
8. **保存结果**: 自动截图并生成访问报告
9. **清理资源**: 优雅关闭环境和清理资源

## 配置说明

### 环境153342专用配置 (config_153342.json)

```json
{
  "morelogin": {
    "profile_id": "153342",           // 指定环境153342
    "profile_name": "环境153342",      // 环境名称
    "use_local_api": true,            // 使用本地API
    "headless_mode": false,           // 显示浏览器窗口
    "window_size": "1920,1080",       // 窗口大小
    "wait_time": 15                   // 等待启动时间
  },
  "crawler": {
    "open_in_new_tab": false,         // 在同一窗口访问
    "take_screenshot": true,          // 启用截图
    "screenshot_dir": "screenshots_153342",  // 专用截图目录
    "wait_for_page_load": 5           // 页面加载等待时间
  },
  "logging": {
    "log_file": "crawler_153342.log" // 专用日志文件
  }
}
```

## 输出文件

### 日志文件
- **文件名**: `crawler_153342.log`
- **内容**: 详细的执行日志，包括启动过程、URL访问结果、错误信息等

### 截图文件
- **目录**: `screenshots_153342/`
- **命名**: `YYYYMMDD_HHMMSS_网站名称.png`
- **内容**: 每个访问页面的完整截图

### 控制台输出示例
```
MoreLogin自动化URL爬虫启动...
使用命令行指定的配置文件ID: 153342
成功加载 2 个URL
正在启动MoreLogin指纹浏览器...
使用MoreLogin本地API启动配置文件...
正在验证配置文件ID: 153342
✓ 找到配置文件: 环境153342 (ID: 153342)
正在启动MoreLogin配置文件: 153342
启动参数: 调试端口=9222, 无头模式=false, 窗口大小=1920,1080
✓ 配置文件已启动，调试端口: 9222
配置文件 153342 已准备就绪，调试端口: 9222
成功连接到MoreLogin浏览器
请确认MoreLogin浏览器已启动并准备就绪，按回车键继续...

开始访问 2 个URL，每个URL间隔 3 秒
进度: 1/2
正在访问: https://www.etsy.com/listing/801131429/...
访问成功: https://www.etsy.com/listing/801131429/... (耗时: 3.2s)
进度: 2/2
正在访问: https://www.etsy.com/listing/1091926003/...
访问成功: https://www.etsy.com/listing/1091926003/... (耗时: 2.8s)

=== 爬取摘要 ===
总URL数: 2
成功: 2
失败: 0
总耗时: 6.0s
平均耗时: 3.0s

程序执行完成，按回车键退出...
```

## 故障排除

### 1. 环境153342不存在
```
错误: 验证配置文件失败: 未找到配置文件ID: 153342
```
**解决方案:**
- 确认MoreLogin中确实存在序号为153342的环境
- 检查环境是否已被删除或重命名
- 联系管理员确认环境状态

### 2. MoreLogin API连接失败
```
错误: API健康检查失败
```
**解决方案:**
- 确认MoreLogin应用程序已启动
- 检查MoreLogin是否开启了本地API功能
- 确认API端口35000未被其他程序占用

### 3. 环境启动失败
```
错误: 启动配置文件失败
```
**解决方案:**
- 检查环境153342是否已被其他程序使用
- 确认有足够的系统资源
- 尝试手动在MoreLogin中启动该环境

### 4. 浏览器连接失败
```
错误: 无法连接到Chrome调试端口
```
**解决方案:**
- 等待更长时间让浏览器完全启动
- 检查防火墙是否阻止了调试端口
- 确认调试端口9222未被占用

## 高级用法

### 自定义URL文件
```bash
# 使用自定义URL文件
go run . -profile 153342 -config config_custom.json
```

### 批量处理
```bash
# 处理多个URL文件
for file in urls_*.txt; do
    echo "处理文件: $file"
    go run . -profile 153342 -config config_153342.json
done
```

### 定时任务
```bash
# 每小时执行一次
0 * * * * cd /path/to/openUrl && ./crawler -profile 153342
```

## 注意事项

1. **环境独占**: 确保环境153342没有被其他程序或用户使用
2. **资源监控**: 监控系统资源使用情况，避免影响其他应用
3. **网络稳定**: 确保网络连接稳定，避免访问中断
4. **日志检查**: 定期检查日志文件，及时发现和解决问题
5. **截图管理**: 定期清理截图目录，避免占用过多磁盘空间

## 技术支持

如果遇到问题，请提供以下信息：
- 错误日志 (crawler_153342.log)
- 系统环境信息
- MoreLogin版本信息
- 具体的错误现象描述
