# MoreLogin自动化URL爬虫

这是一个使用Go语言开发的本地爬虫脚本，结合MoreLogin指纹浏览器自动化来依次打开URL列表中的网址。

## 功能特性

- 🚀 **MoreLogin集成**: 支持本地API和传统启动方式
- 📋 **智能URL管理**: 从文件读取URL列表，支持注释
- 🔄 **重试机制**: 失败自动重试，提高成功率
- ⏱️ **灵活时间控制**: 可配置访问间隔和页面等待时间
- 🌐 **新标签页模式**: 每个URL在独立标签页中打开
- 📸 **截图功能**: 自动截取页面截图并保存
- 📊 **详细统计**: 完整的访问日志和性能统计
- 🛡️ **优雅关闭**: 支持API方式关闭配置文件
- ⚙️ **配置灵活**: 通过JSON文件配置所有参数

## 系统要求

- Go 1.21 或更高版本
- MoreLogin指纹浏览器已安装
- Windows/macOS/Linux操作系统

## 安装和使用

### 1. 克隆或下载项目

```bash
git clone <repository-url>
cd openUrl
```

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 配置MoreLogin

编辑 `config.json` 文件，配置MoreLogin设置：

#### 方法一：使用本地API（推荐）

```json
{
  "morelogin": {
    "use_local_api": true,
    "api_endpoint": "http://127.0.0.1:35000",
    "profile_id": "",
    "debug_port": 9222
  }
}
```

#### 方法二：传统启动方式

```json
{
  "morelogin": {
    "use_local_api": false,
    "execute_path": "C:\\Program Files\\MoreLogin\\MoreLogin.exe",
    "profile_id": "",
    "debug_port": 9222
  }
}
```

**默认路径：**
- Windows: `C:\Program Files\MoreLogin\MoreLogin.exe`
- macOS: `/Applications/MoreLogin.app/Contents/MacOS/MoreLogin`
- Linux: `/opt/MoreLogin/MoreLogin`

### 4. 准备URL文件

在 `url.txt` 文件中添加要访问的URL，每行一个：

```
https://www.example1.com
https://www.example2.com
# 这是注释行，会被忽略
https://www.example3.com
```

### 5. 运行爬虫

```bash
go run .
```

或者编译后运行：

```bash
go build -o crawler
./crawler
```

## 配置说明

### config.json 配置文件

```json
{
  "morelogin": {
    "execute_path": "",          // MoreLogin可执行文件路径，留空自动检测
    "profile_id": "",            // 指纹浏览器配置文件ID，留空使用默认
    "debug_port": 9222           // Chrome调试端口
  },
  "crawler": {
    "delay_between_requests": 3, // URL之间的访问间隔（秒）
    "url_file": "url.txt",       // URL文件路径
    "max_retries": 3,            // 最大重试次数
    "timeout": 30                // 单个URL访问超时时间（秒）
  },
  "logging": {
    "level": "info",             // 日志级别
    "log_file": "crawler.log",   // 日志文件路径
    "console": true              // 是否在控制台显示日志
  }
}
```

## 使用流程

1. **启动程序**：运行 `go run .` 或 `./crawler.exe`
2. **自动连接MoreLogin**：程序会自动连接或启动MoreLogin配置文件
3. **等待确认**：程序会等待用户确认浏览器已准备就绪
4. **开始爬取**：按回车键后开始依次访问URL列表中的网址
5. **自动截图**：如果启用，会自动截取每个页面的截图
6. **查看结果**：程序会显示详细的访问统计和结果摘要
7. **优雅关闭**：程序结束时会自动关闭MoreLogin配置文件

## 注意事项

1. **MoreLogin安装**：确保MoreLogin已正确安装在系统中
2. **防火墙设置**：确保Chrome调试端口（默认9222）未被防火墙阻止
3. **URL格式**：确保URL以 `http://` 或 `https://` 开头
4. **访问间隔**：建议设置适当的访问间隔，避免对目标网站造成压力
5. **网络连接**：确保网络连接稳定

## 故障排除

### 常见问题

1. **MoreLogin启动失败**
   - 检查MoreLogin是否已正确安装
   - 确认配置文件中的路径是否正确
   - 检查是否有足够的系统权限

2. **无法连接到Chrome调试端口**
   - 确认端口9222未被其他程序占用
   - 检查防火墙设置
   - 尝试更改调试端口

3. **URL访问失败**
   - 检查网络连接
   - 确认URL格式正确
   - 检查目标网站是否可访问

### 日志文件

程序会生成详细的日志文件 `crawler.log`，包含：
- 启动和关闭信息
- URL访问结果
- 错误信息和堆栈跟踪
- 性能统计

## 开发和扩展

### 项目结构

```
openUrl/
├── main.go          // 主程序入口
├── crawler.go       // 高级爬虫实现
├── config.go        // 配置文件处理
├── config.json      // 配置文件
├── url.txt          // URL列表文件
├── go.mod           // Go模块文件
└── README.md        // 说明文档
```

### 自定义扩展

可以根据需要扩展功能：
- 添加更多的浏览器操作（截图、表单填写等）
- 实现数据提取和保存
- 添加代理支持
- 集成数据库存储

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
