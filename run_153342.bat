@echo off
echo MoreLogin自动化爬虫 - 启动环境153342
echo ========================================

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go语言环境，请先安装Go
    pause
    exit /b 1
)

REM 检查url.txt文件是否存在
if not exist "url.txt" (
    echo 错误: 未找到url.txt文件
    echo 请创建url.txt文件并添加要访问的URL
    pause
    exit /b 1
)

REM 检查专用配置文件是否存在
if not exist "config_153342.json" (
    echo 警告: 未找到config_153342.json文件，将使用默认配置
    set CONFIG_FILE=config.json
) else (
    set CONFIG_FILE=config_153342.json
    echo 使用专用配置文件: %CONFIG_FILE%
)

REM 安装依赖
echo 正在安装依赖...
go mod tidy

REM 编译程序
echo 正在编译程序...
go build -o crawler.exe .
if %errorlevel% neq 0 (
    echo 编译失败
    pause
    exit /b 1
)

echo.
echo 启动配置:
echo - 目标环境: MoreLogin配置文件 153342
echo - 配置文件: %CONFIG_FILE%
echo - 截图保存: screenshots_153342/
echo - 日志文件: crawler_153342.log
echo.

REM 创建截图目录
if not exist "screenshots_153342" mkdir screenshots_153342

REM 运行程序，指定配置文件ID
echo 正在启动MoreLogin环境153342...
crawler.exe -config %CONFIG_FILE% -profile 153342

echo.
echo 程序执行完成！
echo 请检查以下文件:
echo - 日志文件: crawler_153342.log
echo - 截图目录: screenshots_153342/
echo.

pause
