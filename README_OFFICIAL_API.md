# MoreLogin官方API使用指南

## 🚀 快速开始

### 1. 配置API信息

确保 `config.json` 文件包含正确的API配置：

```json
{
  "morelogin": {
    "env_id": "1958771062629539840",
    "api_id": "1642313569211767",
    "api_key": "53392bd9f81342abb2ee0e3a0875a837",
    "api_endpoint": "https://api.morelogin.com",
    "use_official_api": true,
    "use_local_api": false,
    "wait_time": 15
  }
}
```

### 2. 启动方式

#### 一键启动（推荐）
```cmd
run_official_api.bat
```

#### 命令行启动
```bash
go run .
```

#### 编译后启动
```bash
go build -o crawler.exe .
crawler.exe
```

## 📋 API调用详情

### 启动环境API

**请求信息:**
- **方法**: POST
- **端点**: `/api/env/start`
- **URL**: `https://api.morelogin.com/api/env/start`

**请求头:**
```
Content-Type: application/json
X-Api-Id: 1642313569211767
X-Nonce-Id: {Client-Timestamp}:{Random-UUID}
Authorization: MD5(api_id + X-Nonce-Id + api_key)
```

**请求体:**
```json
{
  "envId": "1958771062629539840"
}
```

**认证算法:**
```
1. 生成时间戳: timestamp = Unix时间戳
2. 生成随机UUID: uuid = 随机UUID字符串
3. 组合Nonce-Id: nonce_id = timestamp + ":" + uuid
4. 计算签名: authorization = MD5(api_id + nonce_id + api_key)
```

### 停止环境API

**请求信息:**
- **方法**: POST
- **端点**: `/api/env/stop`

**请求体:**
```json
{
  "envId": "1958771062629539840"
}
```

## 🔧 程序执行流程

1. **配置验证**: 检查API ID、API Key、环境ID是否配置
2. **API连接测试**: 验证API端点是否可访问
3. **环境启动**: 调用官方API启动指定环境
4. **等待就绪**: 等待环境完全启动并可用
5. **浏览器连接**: 连接到环境的浏览器调试端口
6. **URL访问**: 在环境浏览器窗口中依次访问URL
7. **环境关闭**: 程序结束时通过API关闭环境

## 📊 控制台输出示例

```
MoreLogin自动化URL爬虫启动...
成功加载 2 个URL
正在启动MoreLogin指纹浏览器...
使用MoreLogin官方API启动环境...
测试官方API连接...
✓ 官方API连接正常
正在通过官方API启动环境: 1958771062629539840
请求URL: https://api.morelogin.com/api/env/start
请求头: X-Api-Id=1642313569211767, X-Nonce-Id=1692345678:uuid-string, Authorization=md5hash
请求数据: {"envId":"1958771062629539840"}
API响应状态: 200
API响应内容: {"code":0,"message":"success","data":{"envId":"1958771062629539840","status":"running","debugPort":9222}}
✓ 环境启动成功: 1958771062629539840, 状态: running
等待环境准备就绪: 1958771062629539840
✓ 环境 1958771062629539840 已准备就绪
成功连接到MoreLogin浏览器
请确认MoreLogin浏览器已启动并准备就绪，按回车键继续...

开始访问 2 个URL，每个URL间隔 1 秒
进度: 1/2
正在访问: https://www.etsy.com/listing/801131429/...
访问成功: https://www.etsy.com/listing/801131429/... (耗时: 2.5s)
进度: 2/2
正在访问: https://www.etsy.com/listing/1091926003/...
访问成功: https://www.etsy.com/listing/1091926003/... (耗时: 2.3s)

=== 爬取摘要 ===
总URL数: 2
成功: 2
失败: 0
总耗时: 4.8s
平均耗时: 2.4s

正在通过官方API关闭环境: 1958771062629539840
✓ 环境停止成功: 1958771062629539840
爬虫已关闭
```

## 🛠️ 故障排除

### 1. API认证失败
```
错误: 启动环境失败: API认证失败
```
**解决方案:**
- 检查API ID和API Key是否正确
- 确认API账户是否有效
- 验证时间戳生成是否正确

### 2. 环境ID不存在
```
错误: 启动环境失败: 环境不存在
```
**解决方案:**
- 确认环境ID是否正确
- 检查环境是否已被删除
- 联系管理员确认环境状态

### 3. API端点无法访问
```
错误: API连接测试失败
```
**解决方案:**
- 检查网络连接
- 确认API端点URL是否正确
- 检查防火墙设置

### 4. 环境启动超时
```
错误: 等待环境启动超时
```
**解决方案:**
- 增加等待时间配置
- 检查服务器资源是否充足
- 确认环境配置是否正确

## 🔐 安全注意事项

1. **API密钥保护**: 不要在代码中硬编码API密钥
2. **配置文件安全**: 确保config.json文件权限设置正确
3. **日志清理**: 定期清理包含敏感信息的日志
4. **网络安全**: 使用HTTPS确保API通信安全

## 📈 性能优化

1. **并发控制**: 避免同时启动过多环境
2. **资源监控**: 监控服务器资源使用情况
3. **缓存策略**: 合理使用环境缓存
4. **错误重试**: 实现智能重试机制

## 🔄 API限制

1. **请求频率**: 遵守API调用频率限制
2. **并发数量**: 注意同时运行的环境数量限制
3. **资源配额**: 确认账户资源配额充足
4. **超时设置**: 合理设置API请求超时时间

## 📞 技术支持

如果遇到问题，请提供以下信息：
- 完整的错误日志
- API请求和响应详情
- 环境ID和配置信息
- 网络环境描述

## 🔗 相关链接

- [MoreLogin官方文档](https://www.morelogin.com/docs)
- [API接口文档](https://api.morelogin.com/docs)
- [技术支持](https://support.morelogin.com)
